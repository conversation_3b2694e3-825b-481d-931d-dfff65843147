'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { QrCode, Unlock, Upload } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Import QR code scanner library
import { Html5Qrcode } from 'html5-qrcode';

interface ZATCAData {
  sellerName: string;
  sellerVAT: string;
  dateTime: string;
  total: string;
  vatAmount: string;
}

export function ZatcaQrDecoderTool() {
  const [base64Input, setBase64Input] = useState('');
  const [decodedData, setDecodedData] = useState<ZATCAData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('upload');
  const [isScanning, setIsScanning] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const decodeZATCAQR = (base64Data: string): ZATCAData | null => {
    try {
      console.log('=== ZATCA QR Decoding Debug ===');
      console.log('Input data:', base64Data);
      console.log('Input length:', base64Data.length);

      // Clean the input - remove any whitespace or newlines
      const cleanBase64 = base64Data.trim().replace(/\s/g, '');
      console.log('Cleaned Base64:', cleanBase64);

      // Check if it looks like Base64
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Regex.test(cleanBase64)) {
        console.warn('Input does not look like valid Base64');
        return null;
      }

      // Decode Base64 to binary
      const binaryString = atob(cleanBase64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      console.log('Decoded bytes length:', bytes.length);
      console.log('Decoded bytes (hex):', Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' '));

      // Parse TLV data
      const result: Partial<ZATCAData> = {};
      let offset = 0;
      let tagCount = 0;

      while (offset < bytes.length) {
        if (offset + 2 > bytes.length) {
          console.log('Not enough bytes for tag+length at offset', offset);
          break;
        }

        const tag = bytes[offset];
        const length = bytes[offset + 1];
        offset += 2;
        tagCount++;

        console.log(`Tag ${tagCount}: ${tag}, Length: ${length}, Offset: ${offset}`);

        if (offset + length > bytes.length) {
          console.warn(`Invalid length: ${length} at offset ${offset}, remaining bytes: ${bytes.length - offset}`);
          break;
        }

        const valueBytes = bytes.slice(offset, offset + length);
        const value = new TextDecoder('utf-8').decode(valueBytes);
        offset += length;

        console.log(`Tag ${tag} value: "${value}"`);

        switch (tag) {
          case 1:
            result.sellerName = value;
            break;
          case 2:
            result.sellerVAT = value;
            break;
          case 3:
            result.dateTime = value;
            break;
          case 4:
            result.total = value;
            break;
          case 5:
            result.vatAmount = value;
            break;
          default:
            console.warn(`Unknown tag: ${tag} with value: "${value}"`);
        }
      }

      console.log('Final parsed result:', result);
      console.log('Total tags processed:', tagCount);

      // Check if we have at least one field
      if (result.sellerName || result.sellerVAT || result.dateTime || result.total || result.vatAmount) {
        return result as ZATCAData;
      }

      console.log('No valid ZATCA fields found');
      return null;
    } catch (err) {
      console.error('Decoding error:', err);
      return null;
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setDecodedData(null);
    setIsScanning(true);

    const file = event.target.files?.[0];
    if (!file) {
      setIsScanning(false);
      return;
    }

    console.log('File selected:', file.name, file.type, file.size);

    try {
      const qrCode = new Html5Qrcode('qr-reader-placeholder');
      const decodedText = await qrCode.scanFile(file, false);

      console.log('QR Code scanned text:', decodedText);
      console.log('QR Code text length:', decodedText.length);
      console.log('QR Code first 100 chars:', decodedText.substring(0, 100));

      // Set the base64 input so user can see what was read
      setBase64Input(decodedText);

      // Try to decode as ZATCA QR
      const decoded = decodeZATCAQR(decodedText);
      if (decoded) {
        setDecodedData(decoded);
        toast({
          title: "تم فك التشفير بنجاح!",
          description: "تم استخراج بيانات الفاتورة من QR Code."
        });
      } else {
        // Show what was actually read
        setError(`QR Code تم قراءته ولكن لا يحتوي على بيانات ZATCA صالحة. البيانات المقروءة: "${decodedText.substring(0, 200)}${decodedText.length > 200 ? '...' : ''}"`);
      }
    } catch (err: any) {
      console.error("QR Code scanning error:", err);
      setError(`خطأ في قراءة QR Code: ${err.message || err.toString()}. تأكد من أن الصورة واضحة وتحتوي على QR Code صالح.`);
    } finally {
      setIsScanning(false);
    }
  };

  const handleManualDecode = () => {
    setError(null);
    setDecodedData(null);

    if (!base64Input.trim()) {
      setError('يرجى إدخال البيانات المُشفرة');
      return;
    }

    console.log('Input Base64:', base64Input.trim());

    const decoded = decodeZATCAQR(base64Input.trim());
    if (decoded) {
      setDecodedData(decoded);
      console.log('Successfully decoded:', decoded);
      toast({
        title: "تم فك التشفير بنجاح!",
        description: "تم استخراج بيانات الفاتورة."
      });
    } else {
      setError('فشل في فك تشفير البيانات. تحقق من وحدة التحكم (Console) للمزيد من التفاصيل. تأكد من أن البيانات صحيحة ومتوافقة مع معايير ZATCA');
    }
  };

  const formatDateTime = (dateTime: string) => {
    try {
      const date = new Date(dateTime);
      return date.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateTime;
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <QrCode className="h-5 w-5" />
          فك تشفير QR Code - ZATCA
        </CardTitle>
        <CardDescription>
          أداة لفك تشفير رموز QR الخاصة بالفواتير الإلكترونية المتوافقة مع هيئة الزكاة والضريبة والجمارك
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Placeholder for the QR scanner library */}
        <div id="qr-reader-placeholder" style={{ display: 'none' }}></div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              رفع صورة QR
            </TabsTrigger>
            <TabsTrigger value="manual" className="flex items-center gap-2">
              <QrCode className="h-4 w-4" />
              إدخال يدوي
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            <div className="space-y-2">
              <Label>رفع صورة QR Code</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">اختر صورة تحتوي على QR Code للفاتورة الإلكترونية</p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isScanning}
                  variant="outline"
                >
                  {isScanning ? 'جاري المسح...' : 'اختر صورة'}
                </Button>
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleFileUpload}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="manual" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="base64-input">البيانات المُشفرة (Base64)</Label>
              <Textarea
                id="base64-input"
                placeholder="الصق هنا البيانات المُشفرة من QR Code..."
                value={base64Input}
                onChange={(e) => setBase64Input(e.target.value)}
                className="min-h-[120px] font-mono text-sm"
              />
            </div>

            <Button onClick={handleManualDecode} className="w-full" size="lg">
              <Unlock className="ml-2 h-4 w-4" />
              فك التشفير
            </Button>
          </TabsContent>
        </Tabs>

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm font-medium mb-2">خطأ:</p>
            <p className="text-red-700 text-sm">{error}</p>
            <p className="text-red-600 text-xs mt-2">💡 نصيحة: افتح أدوات المطور (F12) وتحقق من تبويب Console للمزيد من التفاصيل</p>
          </div>
        )}

        {base64Input && !decodedData && !error && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-700 text-sm font-medium mb-2">البيانات المقروءة من QR Code:</p>
            <p className="text-blue-700 text-xs font-mono break-all bg-white p-2 rounded border">
              {base64Input.substring(0, 500)}{base64Input.length > 500 ? '...' : ''}
            </p>
            <p className="text-blue-600 text-xs mt-2">الطول: {base64Input.length} حرف</p>
          </div>
        )}

        {decodedData && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">البيانات المستخرجة:</h3>

            <div className="grid gap-4">
              {decodedData.sellerName && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <Label className="text-sm font-medium text-blue-700">اسم البائع (Tag 1)</Label>
                  <p className="text-blue-900 mt-1">{decodedData.sellerName}</p>
                </div>
              )}

              {decodedData.sellerVAT && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <Label className="text-sm font-medium text-green-700">الرقم الضريبي (Tag 2)</Label>
                  <p className="text-green-900 mt-1 font-mono">{decodedData.sellerVAT}</p>
                </div>
              )}

              {decodedData.dateTime && (
                <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                  <Label className="text-sm font-medium text-purple-700">تاريخ ووقت الفاتورة (Tag 3)</Label>
                  <p className="text-purple-900 mt-1">{formatDateTime(decodedData.dateTime)}</p>
                  <p className="text-purple-600 text-sm mt-1">({decodedData.dateTime})</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {decodedData.total && (
                  <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                    <Label className="text-sm font-medium text-orange-700">المجموع الإجمالي (Tag 4)</Label>
                    <p className="text-orange-900 mt-1 text-lg font-semibold">{decodedData.total} ريال</p>
                  </div>
                )}

                {decodedData.vatAmount && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <Label className="text-sm font-medium text-yellow-700">ضريبة القيمة المضافة (Tag 5)</Label>
                    <p className="text-yellow-900 mt-1 text-lg font-semibold">{decodedData.vatAmount} ريال</p>
                  </div>
                )}
              </div>
            </div>

            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <Label className="text-sm font-medium text-gray-700">ملاحظات</Label>
              <ul className="text-gray-600 text-sm mt-2 space-y-1">
                <li>• تم فك التشفير بنجاح وفقاً لمعايير ZATCA</li>
                <li>• البيانات متوافقة مع تنسيق TLV (Tag-Length-Value)</li>
                <li>• يمكن استخدام هذه البيانات للتحقق من صحة الفاتورة</li>
                <li>• تحقق من وحدة التحكم (Console) للمزيد من التفاصيل التقنية</li>
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
