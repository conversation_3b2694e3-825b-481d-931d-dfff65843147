<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار QR Code - ZATCA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .qr-display {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .step {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار QR Code - ZATCA</h1>
        
        <div class="step">
            <h3>الخطوة 1: إنشاء QR Code تجريبي</h3>
            <button onclick="generateTestQR()">إنشاء QR Code للاختبار</button>
        </div>

        <div id="qrDisplay" class="qr-display">
            <p>اضغط الزر أعلاه لإنشاء QR Code تجريبي</p>
        </div>

        <div class="step">
            <h3>الخطوة 2: معلومات التشخيص</h3>
            <div id="debugInfo" class="debug-info">
                سيتم عرض معلومات التشخيص هنا...
            </div>
        </div>

        <div class="step">
            <h3>الخطوة 3: اختبار فك التشفير</h3>
            <button onclick="testDecoding()">اختبار فك التشفير</button>
            <div id="decodingResult" class="debug-info" style="display: none;">
            </div>
        </div>

        <div class="step">
            <h3>الخطوة 4: تعليمات الاختبار</h3>
            <ol>
                <li>اضغط "إنشاء QR Code للاختبار"</li>
                <li>احفظ الصورة أو التقط لها صورة بالهاتف</li>
                <li>اذهب إلى: <a href="http://localhost:9003/tools/zatca-qr-decoder" target="_blank">أداة فك التشفير</a></li>
                <li>ارفع الصورة في تبويب "رفع صورة QR"</li>
                <li>أو انسخ البيانات المُشفرة والصقها في تبويب "إدخال يدوي"</li>
            </ol>
        </div>
    </div>

    <script>
        let currentQRData = '';

        function toHex(str) {
            let hex = '';
            for (let i = 0; i < str.length; i++) {
                hex += str.charCodeAt(i).toString(16).padStart(2, '0');
            }
            return hex;
        }

        function createTLV(tag, value) {
            const tagHex = tag.toString(16).padStart(2, '0');
            const valueHex = toHex(value);
            const lengthHex = (valueHex.length / 2).toString(16).padStart(2, '0');
            return tagHex + lengthHex + valueHex;
        }

        function generateZATCAQRData() {
            // بيانات تجريبية
            const testData = {
                sellerName: 'شركة الاختبار المحدودة',
                sellerVAT: '123456789012345',
                dateTime: '2024-01-15T14:30:00',
                total: '115.00',
                vatAmount: '15.00'
            };

            // إنشاء TLV data
            const tlvData = 
                createTLV(1, testData.sellerName) +
                createTLV(2, testData.sellerVAT) +
                createTLV(3, testData.dateTime) +
                createTLV(4, testData.total) +
                createTLV(5, testData.vatAmount);

            console.log('TLV Hex Data:', tlvData);

            // تحويل إلى bytes
            const bytes = [];
            for (let i = 0; i < tlvData.length; i += 2) {
                bytes.push(parseInt(tlvData.substring(i, i + 2), 16));
            }

            // تحويل إلى Base64
            const base64Data = btoa(String.fromCharCode.apply(null, bytes));

            // عرض معلومات التشخيص
            const debugInfo = `
=== معلومات التشخيص ===

البيانات الأصلية:
- اسم البائع: ${testData.sellerName}
- الرقم الضريبي: ${testData.sellerVAT}
- التاريخ والوقت: ${testData.dateTime}
- المجموع الإجمالي: ${testData.total}
- ضريبة القيمة المضافة: ${testData.vatAmount}

TLV Hex Data:
${tlvData}

Bytes Array:
[${bytes.join(', ')}]

Base64 Data:
${base64Data}

طول Base64: ${base64Data.length} حرف
            `;

            document.getElementById('debugInfo').textContent = debugInfo;
            currentQRData = base64Data;

            return base64Data;
        }

        function generateTestQR() {
            const qrData = generateZATCAQRData();
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(qrData)}&size=300x300&format=png&margin=0`;
            
            document.getElementById('qrDisplay').innerHTML = `
                <h4>QR Code تجريبي - ZATCA</h4>
                <img src="${qrUrl}" alt="ZATCA QR Code" style="max-width: 300px; border: 1px solid #ccc; margin: 10px;">
                <br>
                <button onclick="copyToClipboard('${qrData}')">نسخ البيانات المُشفرة</button>
                <br><br>
                <strong>البيانات المُشفرة (Base64):</strong><br>
                <textarea style="width: 100%; height: 80px; margin-top: 10px; font-family: monospace; font-size: 12px;" readonly>${qrData}</textarea>
            `;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('تم نسخ البيانات إلى الحافظة!');
            });
        }

        function testDecoding() {
            if (!currentQRData) {
                alert('يرجى إنشاء QR Code أولاً');
                return;
            }

            // محاكاة فك التشفير
            try {
                const cleanBase64 = currentQRData.trim().replace(/\s/g, '');
                const binaryString = atob(cleanBase64);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                let result = '=== نتيجة فك التشفير ===\n\n';
                result += `البيانات المُدخلة: ${currentQRData}\n`;
                result += `طول البيانات: ${currentQRData.length}\n`;
                result += `Bytes المفكوكة: [${Array.from(bytes).join(', ')}]\n`;
                result += `Hex: ${Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ')}\n\n`;

                // فك TLV
                let offset = 0;
                let tagCount = 0;
                const parsedData = {};

                while (offset < bytes.length) {
                    if (offset + 2 > bytes.length) break;

                    const tag = bytes[offset];
                    const length = bytes[offset + 1];
                    offset += 2;
                    tagCount++;

                    if (offset + length > bytes.length) break;

                    const valueBytes = bytes.slice(offset, offset + length);
                    const value = new TextDecoder('utf-8').decode(valueBytes);
                    offset += length;

                    result += `Tag ${tag}: "${value}" (طول: ${length})\n`;
                    parsedData[tag] = value;
                }

                result += `\nإجمالي Tags: ${tagCount}\n`;
                result += `البيانات المستخرجة: ${JSON.stringify(parsedData, null, 2)}`;

                document.getElementById('decodingResult').textContent = result;
                document.getElementById('decodingResult').style.display = 'block';

            } catch (err) {
                document.getElementById('decodingResult').textContent = `خطأ في فك التشفير: ${err.message}`;
                document.getElementById('decodingResult').style.display = 'block';
            }
        }
    </script>
</body>
</html>
